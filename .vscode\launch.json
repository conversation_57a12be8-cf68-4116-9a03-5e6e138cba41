{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "App",
            "request": "launch",
            "mainClass": "App",
            "projectName": "integracaoSaoJose"
        },
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },        
        {
            "type": "java",
            "name": "VMPTC (remote)",
            "request": "attach",
            "projectName": "Integracao Sao Jose",
            "hostName": "icenterv01.ptc.com",
            "port": 55555
        },
        {

            "type": "java",
            "name": "Executar testes JUnit",
            "request": "launch",
            "mainClass": "org.junit.platform.console.ConsoleLauncher",
            "args": [
                "--class-path",
                "bin",
                "--scan-class-path"
            ],
            "vmArgs": "-cp lib/*",
            "projectName": "integracaoSaoJose"
        }
    ]
}