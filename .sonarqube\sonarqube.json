{
  "pathFilters": [
    {
      "pattern": "**/windchill/integracoes/protheus/saojose/**",
      "included": true
    },
    {
      "pattern": "**/windchill/core/**",
      "included": false
    },
    {
      "pattern": "**/windchill/esi/**",
      "included": false
    },
    {
      "pattern": "**/windchill/utils/**",
      "included": false
    },
    {
      "pattern": "**/windchill/mock/**",
      "included": false
    }
  ],
  "rules": {
    //"java:S1118": "on",   // Classes utilitárias sem construtor privado
    "java:S2325": "on",   // Métodos privados que nunca são chamados
    "java:S1854": "on",   // Variáveis atribuídas mas nunca usadas
    "java:S1481": "on",   // Variáveis locais declaradas mas nunca usadas
    "java:S1172": "on",   // Parâmetros de método nunca usados
    "java:S125": "on",    // Código comentado
    "java:S1186": "on",   // Métodos vazios
    "java:S1116": "on"    // Blocos de código vazios
  }
}
