{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Compilar ApiClient e Teste",
            "type": "shell",
            "command": "javac",
            "args": [
                "-cp",
                "D:/Windchill/lib/*;D:/Windchill/src/*",
                "-d",
                "D:/Windchill/codebase",
                "D:/Windchill/src/com/infoaxis/windchill/integracoes/protheus/saoJose/ApiClient.java",
                "D:/Windchill/src-test/com/infoaxis/windchill/integracoes/protheus/saojose/ApiClientTest.java"
            ],
            "options": {
                "shell": {
                    "executable": "cmd.exe",
                    "args": [
                        "/c"
                    ]
                }
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "reveal": "always"
            },
            "problemMatcher": []
        },
        {
            "label": "Executar testes JUnit",
            "type": "shell",
            "command": "java",
            "args": [
                "-jar",
                "D:/Windchill/lib/junit-platform-console-standalone-1.12.1.jar",
                "--class-path",
                "D:/Windchill/codebase;D:/Windchill/lib/junit-platform-console-standalone-1.12.1.jar;D:/Windchill/lib/wiremock-standalone-3.13.1.jar;D:/Windchill/lib/gson-2.10.1.jar",
                "--select-class",
                "com.infoaxis.windchill.integracoes.protheus.saojose.ApiClientTest"
            ],
            "options": {
                "shell": {
                    "executable": "cmd.exe",
                    "args": [
                        "/c"
                    ]
                }
            },
            "presentation": {
                "reveal": "always"
            },
            "problemMatcher": [],
            "group": {
                "kind": "test",
                "isDefault": true
            }
        },
        {
            "label": "Executar testes JUnit - Mock",
            "type": "shell",
            "command": "java",
            "options": {
                "cwd": "D:/Windchill",
                "shell": {
                    "executable": "cmd.exe",
                    "args": [
                        "/c"
                    ]
                }
            },
            "args": [
                "-jar",
                "D:/Windchill/lib/junit-platform-console-standalone-1.12.1.jar",
                "--class-path",
                "D:/Windchill/codebase;D:/Windchill/lib/gson-2.10.1.jar;D:/Windchill/lib/json-20230618.jar;D:/Windchill/lib/junit-platform-console-standalone-1.12.1.jar;D:/Windchill/lib/wiremock-standalone-3.13.1.jar;D:/Windchill/src/test/resources;D:/Windchill/srclib/jakarta.validation-api.jar;D:/Windchill/lib/hibernate-validator-8.0.2.Final.jar;D:/Windchill/lib/jakarta.el-api-5.0.0.jar;D:/Windchill/lib/jakarta.validation-api-3.1.0.jar;D:/Windchill/lib/jboss-logging-3.3.0.Final.jar;D:/Windchill/lib/jakarta.el-4.0.0.jar;D:/Windchill/lib/classmate-1.3.4.jar",
                "--select-class",
                "test.com.infoaxis.windchill.integracoes.protheus.saojose.ApiClientMockTestSuite"
            ],
            "group": {
                "kind": "test"
            },
            "presentation": {
                "reveal": "always"
            },
            "problemMatcher": []
        },
        {
            "label": "Start MockServer",
            "type": "shell",
            "command": "D:\\Desenvolvimento\\WireMockStandalone\\mockserver.bat",
            "args": [
                "start"
            ],
            "problemMatcher": [],
            "presentation": {
                "reveal": "always", // força a abertura do terminal
                "panel": "dedicated", // usa um terminal dedicado
                "showReuseMessage": false,
                "clear": true
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
        },
        {
            "label": "Stop MockServer",
            "type": "shell",
            "command": "D:\\Desenvolvimento\\WireMockStandalone\\mockserver.bat",
            "problemMatcher": [],
            "group": "none"
        }
    ]
}