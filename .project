<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>Integracao Sao Jose</name>
	<comment>Integração da Sáo José <PERSON>: ERP Protheus com o Windchill</comment>
	<projects> </projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.jdt.core.javabuilder</name>
			<arguments> </arguments>
		</buildCommand>
		<buildCommand>
			<name>de.loskutov.FileSync.FSBuilder</name>
			<arguments> </arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>org.eclipse.jdt.core.javanature</nature>
		<!-- <nature>org.eclipse.jdt.groovy.core.groovyNature</nature> -->
	</natures>
	<linkedResources>
		<link>
			<name>codebase</name>
			<type>2</type>
			<location>D:/Windchill/eclipse_codebase</location>
		</link>
		<link>
			<name>src</name>
			<type>2</type>
			<location>D:/Windchill/src</location>
		</link>
		<link>
			<name>src_gen</name>
			<type>2</type>
			<location>D:/Windchill/src_gen</location>
		</link>
	</linkedResources>
	<filteredResources>
		<filter>
			<id>1715630481923</id>
			<name/>
			<type>30</type>
			<matcher>
				<id>org.eclipse.core.resources.regexFilterMatcher</id>
				<arguments>node_modules|\.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__</arguments>
			</matcher>
		</filter>
	</filteredResources>
</projectDescription>