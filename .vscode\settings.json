{
    "java.configuration.updateBuildConfiguration": "automatic",
    "java.project.sourcePaths": [
        "D:/Windchill/src/com/infoaxis/core",
        "D:/Windchill/src/com/infoaxis/esi",
        "D:/Windchill/src/com/infoaxis/integracaoSaoJose",
        "D:/Windchill/src/test"
    ],
    "java.project.referencedLibraries": {
        "include": [
            "D:/Windchill/lib/**/*.jar",
            "D:/Windchill/codebase/WEB-INF/lib/**/*.jar",
            "D:/Windchill/srclib/**/*.jar",
            "D:/Windchill/thirdparty/**/*.jar"
        ],
        "exclude": [
            // Ignora pastas geradas e integrações não relacionadas
            "**/*_gen/",
            "D:/Windchill/src/com/infoaxis/hqimg",
            "D:/Windchill/src/com/infoaxis/integracaoPastre*",
            "D:/Windchill/src/com/infoaxis/integracaoSenior*"
        ]
    },
    "terminal.integrated.profiles.windows": { //Configura o terminal para rodar o windchillShell
        "WindchillShell": {
            "path": "D:\\Windchill\\bin\\windchill.exe",
            "args": [
                "-w",
                "D:\\Windchill",
                "--java=D:\\Java\\bin\\java.exe",
                "shell"
            ]
        }
    },
    "terminal.integrated.defaultProfile.windows": "WindchillShell",
    "terminal.integrated.cwd": "D:\\Windchill\\bin", //Abre o console na pasta o Windchill para compilar direto aqui dentro
    "java.import.maven.enabled": false,
    "java.import.gradle.enabled": false,
    "java.compile.nullAnalysis.mode": "automatic",
    "java.debug.settings.onBuildFailureProceed": true
}